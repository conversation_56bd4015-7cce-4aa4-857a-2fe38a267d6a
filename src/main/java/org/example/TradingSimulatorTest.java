package org.example;

/**
 * Simple test class to demonstrate the trading simulator functionality
 */
public class TradingSimulatorTest {
    public static void main(String[] args) {
        System.out.println("=== Stock Trading Simulator Test ===\n");
        
        // Initialize components
        MarketData marketData = new MarketData();
        TradingEngine tradingEngine = new TradingEngine(marketData);
        PortfolioManager portfolioManager = new PortfolioManager(marketData);
        
        // Create a test user
        User testUser = new User("TestTrader", 10000.0);
        
        // Display initial market data
        System.out.println("Initial Market Data:");
        marketData.displayMarketData();
        
        // Test buying stocks
        System.out.println("\n=== Testing Buy Operations ===");
        TradingEngine.TradeResult buyResult1 = tradingEngine.executeBuyOrder(testUser, "AAPL", 10);
        System.out.println(buyResult1.getMessage());
        
        TradingEngine.TradeResult buyResult2 = tradingEngine.executeBuyOrder(testUser, "GOOGL", 2);
        System.out.println(buyResult2.getMessage());
        
        TradingEngine.TradeResult buyResult3 = tradingEngine.executeBuyOrder(testUser, "TSLA", 5);
        System.out.println(buyResult3.getMessage());
        
        // Display portfolio after purchases
        System.out.println("\n=== Portfolio After Purchases ===");
        portfolioManager.displayPortfolioSummary(testUser);
        
        // Simulate market changes
        System.out.println("\n=== Simulating Market Changes ===");
        marketData.updateAllPrices();
        marketData.updateAllPrices();
        marketData.updateAllPrices();
        
        System.out.println("Updated Market Data:");
        marketData.displayMarketData();
        
        // Display updated portfolio
        System.out.println("\n=== Portfolio After Market Changes ===");
        portfolioManager.displayPortfolioSummary(testUser);
        
        // Test selling stocks
        System.out.println("\n=== Testing Sell Operations ===");
        TradingEngine.TradeResult sellResult1 = tradingEngine.executeSellOrder(testUser, "AAPL", 5);
        System.out.println(sellResult1.getMessage());
        
        // Display final portfolio
        System.out.println("\n=== Final Portfolio ===");
        portfolioManager.displayPortfolioSummary(testUser);
        
        // Display transaction history
        System.out.println("\n=== Transaction History ===");
        portfolioManager.displayTransactionHistory(testUser);
        
        // Display performance metrics
        System.out.println("\n=== Performance Metrics ===");
        portfolioManager.displayPerformanceMetrics(testUser);
        
        // Test file persistence
        System.out.println("\n=== Testing File Persistence ===");
        FileDataPersistence persistence = new FileDataPersistence();
        boolean saved = persistence.saveAllUserData(testUser);
        System.out.println("Data saved: " + saved);
        
        // Load data back
        User loadedUser = persistence.loadAllUserData("TestTrader");
        if (loadedUser != null) {
            System.out.println("Data loaded successfully!");
            System.out.println("Loaded user: " + loadedUser.getUsername());
            System.out.println("Loaded balance: $" + String.format("%.2f", loadedUser.getBalance()));
            System.out.println("Portfolio holdings: " + loadedUser.getPortfolio().getNumberOfStocks() + " stocks");
        } else {
            System.out.println("Failed to load data.");
        }
        
        System.out.println("\n=== Test Complete ===");
    }
}
