package org.example;

import java.util.Map;
import java.util.List;

/**
 * Manages portfolio analysis and performance tracking
 */
public class PortfolioManager {
    private MarketData marketData;

    public PortfolioManager(MarketData marketData) {
        this.marketData = marketData;
    }

    /**
     * Displays detailed portfolio information
     */
    public void displayPortfolioSummary(User user) {
        Portfolio portfolio = user.getPortfolio();
        
        System.out.println("\n" + "=".repeat(90));
        System.out.println("                              PORTFOLIO SUMMARY");
        System.out.println("=".repeat(90));
        System.out.printf("User: %s%n", user.getUsername());
        System.out.printf("Cash Balance: $%.2f%n", user.getBalance());
        System.out.printf("Portfolio Value: $%.2f%n", portfolio.getCurrentValue(marketData));
        System.out.printf("Total Account Value: $%.2f%n", user.getTotalAccountValue(marketData));
        System.out.printf("Total Gain/Loss: $%.2f (%.2f%%)%n", 
                         user.getTotalGainLoss(marketData), 
                         user.getTotalGainLossPercent(marketData));
        System.out.println("-".repeat(90));

        if (portfolio.isEmpty()) {
            System.out.println("No holdings in portfolio.");
        } else {
            System.out.printf("%-8s %-25s %-8s %-12s %-12s %-12s %-12s%n",
                             "Symbol", "Company", "Shares", "Avg Cost", "Current", "Market Val", "Gain/Loss");
            System.out.println("-".repeat(90));

            Map<String, Integer> holdings = portfolio.getHoldings();
            for (Map.Entry<String, Integer> entry : holdings.entrySet()) {
                String symbol = entry.getKey();
                int quantity = entry.getValue();
                Stock stock = marketData.getStock(symbol);
                
                if (stock != null) {
                    double avgCost = portfolio.getAverageCost(symbol);
                    double currentPrice = stock.getCurrentPrice();
                    double marketValue = quantity * currentPrice;
                    double costBasis = quantity * avgCost;
                    double gainLoss = marketValue - costBasis;
                    
                    System.out.printf("%-8s %-25s %-8d $%-11.2f $%-11.2f $%-11.2f $%-11.2f%n",
                                     symbol,
                                     stock.getCompanyName(),
                                     quantity,
                                     avgCost,
                                     currentPrice,
                                     marketValue,
                                     gainLoss);
                }
            }
        }
        System.out.println("=".repeat(90));
    }

    /**
     * Displays transaction history
     */
    public void displayTransactionHistory(User user) {
        List<Transaction> transactions = user.getPortfolio().getTransactionHistory();
        
        System.out.println("\n" + "=".repeat(100));
        System.out.println("                                TRANSACTION HISTORY");
        System.out.println("=".repeat(100));
        
        if (transactions.isEmpty()) {
            System.out.println("No transactions found.");
        } else {
            System.out.printf("%-6s %-8s %-8s %-12s %-12s %-20s%n",
                             "Type", "Symbol", "Shares", "Price", "Total", "Timestamp");
            System.out.println("-".repeat(100));
            
            for (Transaction transaction : transactions) {
                System.out.printf("%-6s %-8s %-8d $%-11.2f $%-11.2f %-20s%n",
                                 transaction.getType().toString(),
                                 transaction.getStockSymbol(),
                                 transaction.getQuantity(),
                                 transaction.getPricePerShare(),
                                 transaction.getTotalAmount(),
                                 transaction.getFormattedTimestamp());
            }
        }
        System.out.println("=".repeat(100));
    }

    /**
     * Displays portfolio performance metrics
     */
    public void displayPerformanceMetrics(User user) {
        Portfolio portfolio = user.getPortfolio();
        
        System.out.println("\n" + "=".repeat(60));
        System.out.println("                 PERFORMANCE METRICS");
        System.out.println("=".repeat(60));
        
        double totalAccountValue = user.getTotalAccountValue(marketData);
        double initialBalance = user.getInitialBalance();
        double totalGainLoss = user.getTotalGainLoss(marketData);
        double totalGainLossPercent = user.getTotalGainLossPercent(marketData);
        
        double portfolioValue = portfolio.getCurrentValue(marketData);
        double portfolioCostBasis = portfolio.getTotalCostBasis();
        double portfolioGainLoss = portfolio.getTotalGainLoss(marketData);
        double portfolioGainLossPercent = portfolio.getGainLossPercent(marketData);
        
        System.out.printf("Initial Balance: $%.2f%n", initialBalance);
        System.out.printf("Current Cash: $%.2f%n", user.getBalance());
        System.out.printf("Portfolio Cost Basis: $%.2f%n", portfolioCostBasis);
        System.out.printf("Portfolio Market Value: $%.2f%n", portfolioValue);
        System.out.printf("Portfolio Gain/Loss: $%.2f (%.2f%%)%n", portfolioGainLoss, portfolioGainLossPercent);
        System.out.printf("Total Account Value: $%.2f%n", totalAccountValue);
        System.out.printf("Total Account Gain/Loss: $%.2f (%.2f%%)%n", totalGainLoss, totalGainLossPercent);
        System.out.printf("Number of Holdings: %d%n", portfolio.getNumberOfStocks());
        System.out.printf("Number of Transactions: %d%n", portfolio.getTransactionHistory().size());
        
        System.out.println("=".repeat(60));
    }

    /**
     * Gets a summary string of portfolio performance
     */
    public String getPortfolioSummary(User user) {
        Portfolio portfolio = user.getPortfolio();
        double totalValue = user.getTotalAccountValue(marketData);
        double gainLoss = user.getTotalGainLoss(marketData);
        double gainLossPercent = user.getTotalGainLossPercent(marketData);
        
        return String.format("Total Value: $%.2f | Gain/Loss: $%.2f (%.2f%%) | Holdings: %d",
                           totalValue, gainLoss, gainLossPercent, portfolio.getNumberOfStocks());
    }
}
