package org.example;

import java.io.*;
import java.util.Map;
import java.util.List;

/**
 * Handles file I/O operations for persisting user data and portfolio information
 */
public class FileDataPersistence {
    private static final String DATA_DIRECTORY = "trading_data";
    private static final String USER_FILE_SUFFIX = "_user_data.txt";
    private static final String PORTFOLIO_FILE_SUFFIX = "_portfolio_data.txt";
    private static final String TRANSACTIONS_FILE_SUFFIX = "_transactions.txt";

    public FileDataPersistence() {
        createDataDirectory();
    }

    /**
     * Creates the data directory if it doesn't exist
     */
    private void createDataDirectory() {
        File directory = new File(DATA_DIRECTORY);
        if (!directory.exists()) {
            directory.mkdirs();
        }
    }

    /**
     * Saves user data to file
     */
    public boolean saveUserData(User user) {
        try {
            String filename = DATA_DIRECTORY + File.separator + user.getUsername() + USER_FILE_SUFFIX;
            try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
                writer.println("USERNAME:" + user.getUsername());
                writer.println("BALANCE:" + user.getBalance());
                writer.println("INITIAL_BALANCE:" + user.getInitialBalance());
            }
            return true;
        } catch (IOException e) {
            System.err.println("Error saving user data: " + e.getMessage());
            return false;
        }
    }

    /**
     * Saves portfolio data to file
     */
    public boolean savePortfolioData(User user) {
        try {
            String filename = DATA_DIRECTORY + File.separator + user.getUsername() + PORTFOLIO_FILE_SUFFIX;
            try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
                Portfolio portfolio = user.getPortfolio();
                Map<String, Integer> holdings = portfolio.getHoldings();
                
                for (Map.Entry<String, Integer> entry : holdings.entrySet()) {
                    String symbol = entry.getKey();
                    int quantity = entry.getValue();
                    double avgCost = portfolio.getAverageCost(symbol);
                    writer.println(symbol + ":" + quantity + ":" + avgCost);
                }
            }
            return true;
        } catch (IOException e) {
            System.err.println("Error saving portfolio data: " + e.getMessage());
            return false;
        }
    }

    /**
     * Saves transaction history to file
     */
    public boolean saveTransactionHistory(User user) {
        try {
            String filename = DATA_DIRECTORY + File.separator + user.getUsername() + TRANSACTIONS_FILE_SUFFIX;
            try (PrintWriter writer = new PrintWriter(new FileWriter(filename))) {
                List<Transaction> transactions = user.getPortfolio().getTransactionHistory();
                
                for (Transaction transaction : transactions) {
                    writer.println(String.format("%s:%s:%d:%.2f:%s",
                                                transaction.getType().toString(),
                                                transaction.getStockSymbol(),
                                                transaction.getQuantity(),
                                                transaction.getPricePerShare(),
                                                transaction.getFormattedTimestamp()));
                }
            }
            return true;
        } catch (IOException e) {
            System.err.println("Error saving transaction history: " + e.getMessage());
            return false;
        }
    }

    /**
     * Loads user data from file
     */
    public User loadUserData(String username) {
        try {
            String filename = DATA_DIRECTORY + File.separator + username + USER_FILE_SUFFIX;
            File file = new File(filename);
            
            if (!file.exists()) {
                return null; // User data not found
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(filename))) {
                String line;
                String loadedUsername = null;
                double balance = 0.0;
                double initialBalance = 0.0;

                while ((line = reader.readLine()) != null) {
                    String[] parts = line.split(":", 2);
                    if (parts.length == 2) {
                        switch (parts[0]) {
                            case "USERNAME":
                                loadedUsername = parts[1];
                                break;
                            case "BALANCE":
                                balance = Double.parseDouble(parts[1]);
                                break;
                            case "INITIAL_BALANCE":
                                initialBalance = Double.parseDouble(parts[1]);
                                break;
                        }
                    }
                }

                if (loadedUsername != null) {
                    User user = new User(loadedUsername, initialBalance);
                    user.setBalance(balance);
                    return user;
                }
            }
        } catch (IOException | NumberFormatException e) {
            System.err.println("Error loading user data: " + e.getMessage());
        }
        return null;
    }

    /**
     * Loads portfolio data from file
     */
    public boolean loadPortfolioData(User user) {
        try {
            String filename = DATA_DIRECTORY + File.separator + user.getUsername() + PORTFOLIO_FILE_SUFFIX;
            File file = new File(filename);
            
            if (!file.exists()) {
                return true; // No portfolio data to load, but not an error
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(filename))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String[] parts = line.split(":");
                    if (parts.length == 3) {
                        String symbol = parts[0];
                        int quantity = Integer.parseInt(parts[1]);
                        double avgCost = Double.parseDouble(parts[2]);
                        
                        // Manually add to portfolio (simulating historical purchases)
                        Portfolio portfolio = user.getPortfolio();
                        portfolio.addShares(symbol, quantity, avgCost);
                    }
                }
            }
            return true;
        } catch (IOException | NumberFormatException e) {
            System.err.println("Error loading portfolio data: " + e.getMessage());
            return false;
        }
    }

    /**
     * Saves all user data (user info, portfolio, and transactions)
     */
    public boolean saveAllUserData(User user) {
        boolean userSaved = saveUserData(user);
        boolean portfolioSaved = savePortfolioData(user);
        boolean transactionsSaved = saveTransactionHistory(user);
        
        return userSaved && portfolioSaved && transactionsSaved;
    }

    /**
     * Loads all user data (user info and portfolio)
     */
    public User loadAllUserData(String username) {
        User user = loadUserData(username);
        if (user != null) {
            loadPortfolioData(user);
        }
        return user;
    }

    /**
     * Checks if user data exists
     */
    public boolean userDataExists(String username) {
        String filename = DATA_DIRECTORY + File.separator + username + USER_FILE_SUFFIX;
        return new File(filename).exists();
    }

    /**
     * Deletes all user data files
     */
    public boolean deleteUserData(String username) {
        try {
            String userFile = DATA_DIRECTORY + File.separator + username + USER_FILE_SUFFIX;
            String portfolioFile = DATA_DIRECTORY + File.separator + username + PORTFOLIO_FILE_SUFFIX;
            String transactionsFile = DATA_DIRECTORY + File.separator + username + TRANSACTIONS_FILE_SUFFIX;
            
            boolean deleted = true;
            File file1 = new File(userFile);
            File file2 = new File(portfolioFile);
            File file3 = new File(transactionsFile);
            
            if (file1.exists()) deleted &= file1.delete();
            if (file2.exists()) deleted &= file2.delete();
            if (file3.exists()) deleted &= file3.delete();
            
            return deleted;
        } catch (Exception e) {
            System.err.println("Error deleting user data: " + e.getMessage());
            return false;
        }
    }
}
