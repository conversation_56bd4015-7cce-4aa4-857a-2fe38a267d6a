package org.example;

import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;

/**
 * Manages a user's stock holdings and calculates portfolio value
 */
public class Portfolio {
    private Map<String, Integer> holdings; // symbol -> quantity
    private Map<String, Double> averageCosts; // symbol -> average cost per share
    private List<Transaction> transactionHistory;
    private double initialValue;

    public Portfolio() {
        this.holdings = new HashMap<>();
        this.averageCosts = new HashMap<>();
        this.transactionHistory = new ArrayList<>();
        this.initialValue = 0.0;
    }

    /**
     * Adds shares to the portfolio (for buy transactions)
     */
    public void addShares(String symbol, int quantity, double pricePerShare) {
        int currentQuantity = holdings.getOrDefault(symbol, 0);
        double currentAverageCost = averageCosts.getOrDefault(symbol, 0.0);
        
        // Calculate new average cost
        double totalCost = (currentQuantity * currentAverageCost) + (quantity * pricePerShare);
        int newQuantity = currentQuantity + quantity;
        double newAverageCost = totalCost / newQuantity;
        
        holdings.put(symbol, newQuantity);
        averageCosts.put(symbol, newAverageCost);
        
        // Add transaction
        Transaction transaction = new Transaction(symbol, Transaction.TransactionType.BUY, quantity, pricePerShare);
        transactionHistory.add(transaction);
    }

    /**
     * Removes shares from the portfolio (for sell transactions)
     */
    public boolean removeShares(String symbol, int quantity, double pricePerShare) {
        int currentQuantity = holdings.getOrDefault(symbol, 0);
        
        if (currentQuantity < quantity) {
            return false; // Not enough shares to sell
        }
        
        int newQuantity = currentQuantity - quantity;
        if (newQuantity == 0) {
            holdings.remove(symbol);
            averageCosts.remove(symbol);
        } else {
            holdings.put(symbol, newQuantity);
        }
        
        // Add transaction
        Transaction transaction = new Transaction(symbol, Transaction.TransactionType.SELL, quantity, pricePerShare);
        transactionHistory.add(transaction);
        
        return true;
    }

    /**
     * Gets the quantity of shares for a specific stock
     */
    public int getQuantity(String symbol) {
        return holdings.getOrDefault(symbol, 0);
    }

    /**
     * Gets the average cost per share for a specific stock
     */
    public double getAverageCost(String symbol) {
        return averageCosts.getOrDefault(symbol, 0.0);
    }

    /**
     * Calculates the current total value of the portfolio
     */
    public double getCurrentValue(MarketData marketData) {
        double totalValue = 0.0;
        for (Map.Entry<String, Integer> entry : holdings.entrySet()) {
            String symbol = entry.getKey();
            int quantity = entry.getValue();
            Stock stock = marketData.getStock(symbol);
            if (stock != null) {
                totalValue += quantity * stock.getCurrentPrice();
            }
        }
        return totalValue;
    }

    /**
     * Calculates the total cost basis of the portfolio
     */
    public double getTotalCostBasis() {
        double totalCost = 0.0;
        for (Map.Entry<String, Integer> entry : holdings.entrySet()) {
            String symbol = entry.getKey();
            int quantity = entry.getValue();
            double averageCost = averageCosts.get(symbol);
            totalCost += quantity * averageCost;
        }
        return totalCost;
    }

    /**
     * Calculates the total gain/loss of the portfolio
     */
    public double getTotalGainLoss(MarketData marketData) {
        return getCurrentValue(marketData) - getTotalCostBasis();
    }

    /**
     * Calculates the percentage gain/loss of the portfolio
     */
    public double getGainLossPercent(MarketData marketData) {
        double costBasis = getTotalCostBasis();
        if (costBasis == 0) return 0.0;
        return (getTotalGainLoss(marketData) / costBasis) * 100;
    }

    /**
     * Gets all holdings in the portfolio
     */
    public Map<String, Integer> getHoldings() {
        return new HashMap<>(holdings);
    }

    /**
     * Gets transaction history
     */
    public List<Transaction> getTransactionHistory() {
        return new ArrayList<>(transactionHistory);
    }

    /**
     * Checks if the portfolio has any holdings
     */
    public boolean isEmpty() {
        return holdings.isEmpty();
    }

    /**
     * Gets the number of different stocks in the portfolio
     */
    public int getNumberOfStocks() {
        return holdings.size();
    }
}
