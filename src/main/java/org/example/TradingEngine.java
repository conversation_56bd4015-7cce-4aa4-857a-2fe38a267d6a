package org.example;

/**
 * Handles buy and sell operations with validation
 */
public class TradingEngine {
    private MarketData marketData;

    public TradingEngine(MarketData marketData) {
        this.marketData = marketData;
    }

    /**
     * Executes a buy order
     */
    public TradeResult executeBuyOrder(User user, String symbol, int quantity) {
        // Validate inputs
        if (quantity <= 0) {
            return new TradeResult(false, "Quantity must be positive");
        }

        Stock stock = marketData.getStock(symbol);
        if (stock == null) {
            return new TradeResult(false, "Stock symbol not found: " + symbol);
        }

        double totalCost = quantity * stock.getCurrentPrice();
        if (user.getBalance() < totalCost) {
            return new TradeResult(false, 
                String.format("Insufficient funds. Required: $%.2f, Available: $%.2f", 
                             totalCost, user.getBalance()));
        }

        // Execute the trade
        boolean success = user.buyShares(symbol, quantity, stock.getCurrentPrice());
        if (success) {
            return new TradeResult(true, 
                String.format("Successfully bought %d shares of %s at $%.2f per share (Total: $%.2f)",
                             quantity, symbol, stock.getCurrentPrice(), totalCost));
        } else {
            return new TradeResult(false, "Trade execution failed");
        }
    }

    /**
     * Executes a sell order
     */
    public TradeResult executeSellOrder(User user, String symbol, int quantity) {
        // Validate inputs
        if (quantity <= 0) {
            return new TradeResult(false, "Quantity must be positive");
        }

        Stock stock = marketData.getStock(symbol);
        if (stock == null) {
            return new TradeResult(false, "Stock symbol not found: " + symbol);
        }

        int availableShares = user.getPortfolio().getQuantity(symbol);
        if (availableShares < quantity) {
            return new TradeResult(false, 
                String.format("Insufficient shares. Requested: %d, Available: %d", 
                             quantity, availableShares));
        }

        // Execute the trade
        double totalRevenue = quantity * stock.getCurrentPrice();
        boolean success = user.sellShares(symbol, quantity, stock.getCurrentPrice());
        if (success) {
            return new TradeResult(true, 
                String.format("Successfully sold %d shares of %s at $%.2f per share (Total: $%.2f)",
                             quantity, symbol, stock.getCurrentPrice(), totalRevenue));
        } else {
            return new TradeResult(false, "Trade execution failed");
        }
    }

    /**
     * Gets a quote for a stock
     */
    public String getQuote(String symbol) {
        Stock stock = marketData.getStock(symbol);
        if (stock == null) {
            return "Stock symbol not found: " + symbol;
        }

        double change = stock.getPriceChange();
        double changePercent = stock.getPriceChangePercent();
        String changeStr = String.format("%.2f", change);
        String changePercentStr = String.format("%.2f%%", changePercent);
        
        if (change > 0) {
            changeStr = "+" + changeStr;
            changePercentStr = "+" + changePercentStr;
        }

        return String.format("%s (%s): $%.2f (Change: $%s, %s)",
                           stock.getCompanyName(), stock.getSymbol(), 
                           stock.getCurrentPrice(), changeStr, changePercentStr);
    }

    /**
     * Calculates the cost of a potential buy order
     */
    public double calculateBuyCost(String symbol, int quantity) {
        Stock stock = marketData.getStock(symbol);
        if (stock == null) return 0.0;
        return quantity * stock.getCurrentPrice();
    }

    /**
     * Calculates the revenue of a potential sell order
     */
    public double calculateSellRevenue(String symbol, int quantity) {
        Stock stock = marketData.getStock(symbol);
        if (stock == null) return 0.0;
        return quantity * stock.getCurrentPrice();
    }

    /**
     * Inner class to represent trade results
     */
    public static class TradeResult {
        private boolean success;
        private String message;

        public TradeResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }
    }
}
