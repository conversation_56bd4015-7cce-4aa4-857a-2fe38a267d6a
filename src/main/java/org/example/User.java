package org.example;

/**
 * Represents a user in the trading system with balance and portfolio
 */
public class User {
    private String username;
    private double balance;
    private Portfolio portfolio;
    private double initialBalance;

    public User(String username, double initialBalance) {
        this.username = username;
        this.balance = initialBalance;
        this.initialBalance = initialBalance;
        this.portfolio = new Portfolio();
    }

    /**
     * Attempts to buy shares if user has sufficient balance
     */
    public boolean buyShares(String symbol, int quantity, double pricePerShare) {
        double totalCost = quantity * pricePerShare;
        
        if (balance >= totalCost) {
            balance -= totalCost;
            portfolio.addShares(symbol, quantity, pricePerShare);
            return true;
        }
        return false;
    }

    /**
     * Attempts to sell shares if user has sufficient quantity
     */
    public boolean sellShares(String symbol, int quantity, double pricePerShare) {
        if (portfolio.removeShares(symbol, quantity, pricePerShare)) {
            double totalRevenue = quantity * pricePerShare;
            balance += totalRevenue;
            return true;
        }
        return false;
    }

    /**
     * Gets the total account value (balance + portfolio value)
     */
    public double getTotalAccountValue(MarketData marketData) {
        return balance + portfolio.getCurrentValue(marketData);
    }

    /**
     * Gets the total gain/loss from initial balance
     */
    public double getTotalGainLoss(MarketData marketData) {
        return getTotalAccountValue(marketData) - initialBalance;
    }

    /**
     * Gets the percentage gain/loss from initial balance
     */
    public double getTotalGainLossPercent(MarketData marketData) {
        if (initialBalance == 0) return 0.0;
        return (getTotalGainLoss(marketData) / initialBalance) * 100;
    }

    // Getters and Setters
    public String getUsername() {
        return username;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public Portfolio getPortfolio() {
        return portfolio;
    }

    public double getInitialBalance() {
        return initialBalance;
    }

    @Override
    public String toString() {
        return String.format("User: %s, Balance: $%.2f", username, balance);
    }
}
