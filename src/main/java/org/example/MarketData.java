package org.example;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * Manages market data and stock information
 */
public class MarketData {
    private Map<String, Stock> stocks;

    public MarketData() {
        this.stocks = new HashMap<>();
        initializeDefaultStocks();
    }

    /**
     * Initializes the market with some default stocks
     */
    private void initializeDefaultStocks() {
        addStock(new Stock("AAPL", "Apple Inc.", 150.00));
        addStock(new Stock("GOOGL", "Alphabet Inc.", 2500.00));
        addStock(new Stock("MSFT", "Microsoft Corporation", 300.00));
        addStock(new Stock("AMZN", "Amazon.com Inc.", 3200.00));
        addStock(new Stock("TSLA", "Tesla Inc.", 800.00));
        addStock(new Stock("META", "Meta Platforms Inc.", 320.00));
        addStock(new Stock("NVDA", "NVIDIA Corporation", 450.00));
        addStock(new Stock("NFLX", "Netflix Inc.", 400.00));
        addStock(new Stock("AMD", "Advanced Micro Devices", 90.00));
        addStock(new Stock("INTC", "Intel Corporation", 55.00));
    }

    /**
     * Adds a stock to the market
     */
    public void addStock(Stock stock) {
        stocks.put(stock.getSymbol(), stock);
    }

    /**
     * Gets a stock by symbol
     */
    public Stock getStock(String symbol) {
        return stocks.get(symbol.toUpperCase());
    }

    /**
     * Gets all available stocks
     */
    public List<Stock> getAllStocks() {
        return new ArrayList<>(stocks.values());
    }

    /**
     * Updates all stock prices (simulates market movement)
     */
    public void updateAllPrices() {
        for (Stock stock : stocks.values()) {
            stock.updatePrice();
        }
    }

    /**
     * Checks if a stock symbol exists in the market
     */
    public boolean hasStock(String symbol) {
        return stocks.containsKey(symbol.toUpperCase());
    }

    /**
     * Gets the number of stocks in the market
     */
    public int getStockCount() {
        return stocks.size();
    }

    /**
     * Displays market data in a formatted table
     */
    public void displayMarketData() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("                           MARKET DATA");
        System.out.println("=".repeat(80));
        System.out.printf("%-8s %-25s %-12s %-12s %-10s%n", 
                         "Symbol", "Company", "Price", "Change", "Change %");
        System.out.println("-".repeat(80));

        for (Stock stock : stocks.values()) {
            double change = stock.getPriceChange();
            double changePercent = stock.getPriceChangePercent();
            String changeStr = String.format("$%.2f", change);
            String changePercentStr = String.format("%.2f%%", changePercent);
            
            // Add color indicators (+ for positive, - for negative)
            if (change > 0) {
                changeStr = "+" + changeStr;
                changePercentStr = "+" + changePercentStr;
            }

            System.out.printf("%-8s %-25s $%-11.2f %-12s %-10s%n",
                             stock.getSymbol(),
                             stock.getCompanyName(),
                             stock.getCurrentPrice(),
                             changeStr,
                             changePercentStr);
        }
        System.out.println("=".repeat(80));
    }
}
