package org.example;

import java.util.Scanner;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Main application class that provides the user interface for the stock trading simulation
 */
public class TradingSimulator {
    private MarketData marketData;
    private TradingEngine tradingEngine;
    private PortfolioManager portfolioManager;
    private FileDataPersistence dataPersistence;
    private User currentUser;
    private Scanner scanner;
    private Timer marketUpdateTimer;
    private boolean isRunning;

    public TradingSimulator() {
        this.marketData = new MarketData();
        this.tradingEngine = new TradingEngine(marketData);
        this.portfolioManager = new PortfolioManager(marketData);
        this.dataPersistence = new FileDataPersistence();
        this.scanner = new Scanner(System.in);
        this.isRunning = false;
    }

    /**
     * Starts the trading simulation
     */
    public void start() {
        System.out.println("=".repeat(60));
        System.out.println("        WELCOME TO STOCK TRADING SIMULATOR");
        System.out.println("=".repeat(60));
        
        // User login/registration
        handleUserLogin();
        
        if (currentUser != null) {
            isRunning = true;
            startMarketUpdates();
            showMainMenu();
        }
        
        cleanup();
    }

    /**
     * Handles user login or registration
     */
    private void handleUserLogin() {
        System.out.println("\n1. Login with existing account");
        System.out.println("2. Create new account");
        System.out.print("Choose option (1-2): ");
        
        try {
            int choice = Integer.parseInt(scanner.nextLine().trim());
            
            switch (choice) {
                case 1:
                    loginUser();
                    break;
                case 2:
                    createNewUser();
                    break;
                default:
                    System.out.println("Invalid choice. Creating new account...");
                    createNewUser();
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid input. Creating new account...");
            createNewUser();
        }
    }

    /**
     * Logs in an existing user
     */
    private void loginUser() {
        System.out.print("Enter username: ");
        String username = scanner.nextLine().trim();
        
        if (dataPersistence.userDataExists(username)) {
            currentUser = dataPersistence.loadAllUserData(username);
            if (currentUser != null) {
                System.out.println("Welcome back, " + username + "!");
                System.out.println(portfolioManager.getPortfolioSummary(currentUser));
            } else {
                System.out.println("Error loading user data. Creating new account...");
                createNewUser();
            }
        } else {
            System.out.println("User not found. Creating new account...");
            createNewUser();
        }
    }

    /**
     * Creates a new user account
     */
    private void createNewUser() {
        System.out.print("Enter username: ");
        String username = scanner.nextLine().trim();
        
        if (username.isEmpty()) {
            System.out.println("Username cannot be empty. Using default username 'trader'.");
            username = "trader";
        }
        
        System.out.print("Enter initial balance (default $10000): ");
        String balanceInput = scanner.nextLine().trim();
        
        double initialBalance = 10000.0;
        if (!balanceInput.isEmpty()) {
            try {
                initialBalance = Double.parseDouble(balanceInput);
                if (initialBalance < 0) {
                    System.out.println("Balance cannot be negative. Using default $10000.");
                    initialBalance = 10000.0;
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid balance. Using default $10000.");
            }
        }
        
        currentUser = new User(username, initialBalance);
        System.out.println("Account created successfully!");
        System.out.printf("Welcome, %s! Starting balance: $%.2f%n", username, initialBalance);
    }

    /**
     * Starts automatic market updates
     */
    private void startMarketUpdates() {
        marketUpdateTimer = new Timer(true);
        marketUpdateTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                if (isRunning) {
                    marketData.updateAllPrices();
                }
            }
        }, 5000, 10000); // Update every 10 seconds after initial 5 second delay
    }

    /**
     * Shows the main menu and handles user input
     */
    private void showMainMenu() {
        while (isRunning) {
            System.out.println("\n" + "=".repeat(50));
            System.out.println("              MAIN MENU");
            System.out.println("=".repeat(50));
            System.out.println("1. View Market Data");
            System.out.println("2. Get Stock Quote");
            System.out.println("3. Buy Stocks");
            System.out.println("4. Sell Stocks");
            System.out.println("5. View Portfolio");
            System.out.println("6. View Transaction History");
            System.out.println("7. View Performance Metrics");
            System.out.println("8. Save Data");
            System.out.println("9. Exit");
            System.out.println("=".repeat(50));
            System.out.print("Choose option (1-9): ");
            
            try {
                int choice = Integer.parseInt(scanner.nextLine().trim());
                handleMenuChoice(choice);
            } catch (NumberFormatException e) {
                System.out.println("Invalid input. Please enter a number between 1-9.");
            }
        }
    }

    /**
     * Handles menu choice selection
     */
    private void handleMenuChoice(int choice) {
        switch (choice) {
            case 1:
                marketData.displayMarketData();
                break;
            case 2:
                getStockQuote();
                break;
            case 3:
                buyStocks();
                break;
            case 4:
                sellStocks();
                break;
            case 5:
                portfolioManager.displayPortfolioSummary(currentUser);
                break;
            case 6:
                portfolioManager.displayTransactionHistory(currentUser);
                break;
            case 7:
                portfolioManager.displayPerformanceMetrics(currentUser);
                break;
            case 8:
                saveUserData();
                break;
            case 9:
                exitApplication();
                break;
            default:
                System.out.println("Invalid choice. Please select 1-9.");
        }
    }

    /**
     * Gets a stock quote
     */
    private void getStockQuote() {
        System.out.print("Enter stock symbol: ");
        String symbol = scanner.nextLine().trim().toUpperCase();
        
        if (symbol.isEmpty()) {
            System.out.println("Symbol cannot be empty.");
            return;
        }
        
        String quote = tradingEngine.getQuote(symbol);
        System.out.println("\n" + quote);
    }

    /**
     * Handles stock buying
     */
    private void buyStocks() {
        System.out.print("Enter stock symbol: ");
        String symbol = scanner.nextLine().trim().toUpperCase();
        
        if (symbol.isEmpty()) {
            System.out.println("Symbol cannot be empty.");
            return;
        }
        
        if (!marketData.hasStock(symbol)) {
            System.out.println("Stock symbol not found: " + symbol);
            return;
        }
        
        Stock stock = marketData.getStock(symbol);
        System.out.printf("Current price of %s: $%.2f%n", symbol, stock.getCurrentPrice());
        
        System.out.print("Enter quantity to buy: ");
        try {
            int quantity = Integer.parseInt(scanner.nextLine().trim());
            
            if (quantity <= 0) {
                System.out.println("Quantity must be positive.");
                return;
            }
            
            double totalCost = tradingEngine.calculateBuyCost(symbol, quantity);
            System.out.printf("Total cost: $%.2f%n", totalCost);
            System.out.printf("Your current balance: $%.2f%n", currentUser.getBalance());
            
            System.out.print("Confirm purchase? (y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if (confirm.equals("y") || confirm.equals("yes")) {
                TradingEngine.TradeResult result = tradingEngine.executeBuyOrder(currentUser, symbol, quantity);
                System.out.println("\n" + result.getMessage());
            } else {
                System.out.println("Purchase cancelled.");
            }
            
        } catch (NumberFormatException e) {
            System.out.println("Invalid quantity. Please enter a valid number.");
        }
    }

    /**
     * Handles stock selling
     */
    private void sellStocks() {
        System.out.print("Enter stock symbol: ");
        String symbol = scanner.nextLine().trim().toUpperCase();
        
        if (symbol.isEmpty()) {
            System.out.println("Symbol cannot be empty.");
            return;
        }
        
        if (!marketData.hasStock(symbol)) {
            System.out.println("Stock symbol not found: " + symbol);
            return;
        }
        
        int availableShares = currentUser.getPortfolio().getQuantity(symbol);
        if (availableShares == 0) {
            System.out.println("You don't own any shares of " + symbol);
            return;
        }
        
        Stock stock = marketData.getStock(symbol);
        System.out.printf("Current price of %s: $%.2f%n", symbol, stock.getCurrentPrice());
        System.out.printf("You own %d shares%n", availableShares);
        
        System.out.print("Enter quantity to sell: ");
        try {
            int quantity = Integer.parseInt(scanner.nextLine().trim());
            
            if (quantity <= 0) {
                System.out.println("Quantity must be positive.");
                return;
            }
            
            if (quantity > availableShares) {
                System.out.println("You don't have enough shares to sell.");
                return;
            }
            
            double totalRevenue = tradingEngine.calculateSellRevenue(symbol, quantity);
            System.out.printf("Total revenue: $%.2f%n", totalRevenue);
            
            System.out.print("Confirm sale? (y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if (confirm.equals("y") || confirm.equals("yes")) {
                TradingEngine.TradeResult result = tradingEngine.executeSellOrder(currentUser, symbol, quantity);
                System.out.println("\n" + result.getMessage());
            } else {
                System.out.println("Sale cancelled.");
            }
            
        } catch (NumberFormatException e) {
            System.out.println("Invalid quantity. Please enter a valid number.");
        }
    }

    /**
     * Saves user data to files
     */
    private void saveUserData() {
        System.out.print("Saving data...");
        boolean success = dataPersistence.saveAllUserData(currentUser);
        
        if (success) {
            System.out.println(" Done!");
            System.out.println("All data saved successfully.");
        } else {
            System.out.println(" Failed!");
            System.out.println("Error saving data. Please try again.");
        }
    }

    /**
     * Exits the application
     */
    private void exitApplication() {
        System.out.print("Save data before exiting? (y/n): ");
        String save = scanner.nextLine().trim().toLowerCase();
        
        if (save.equals("y") || save.equals("yes")) {
            saveUserData();
        }
        
        System.out.println("Thank you for using Stock Trading Simulator!");
        System.out.println("Final " + portfolioManager.getPortfolioSummary(currentUser));
        isRunning = false;
    }

    /**
     * Cleanup resources
     */
    private void cleanup() {
        if (marketUpdateTimer != null) {
            marketUpdateTimer.cancel();
        }
        scanner.close();
    }
}
