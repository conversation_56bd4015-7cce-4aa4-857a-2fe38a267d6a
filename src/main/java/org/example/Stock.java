package org.example;

import java.util.Random;

/**
 * Represents a stock with symbol, company name, and current price
 */
public class Stock {
    private String symbol;
    private String companyName;
    private double currentPrice;
    private double previousPrice;
    private Random random;

    public Stock(String symbol, String companyName, double initialPrice) {
        this.symbol = symbol;
        this.companyName = companyName;
        this.currentPrice = initialPrice;
        this.previousPrice = initialPrice;
        this.random = new Random();
    }

    /**
     * Simulates price fluctuation based on market volatility
     */
    public void updatePrice() {
        this.previousPrice = this.currentPrice;
        // Simulate price change between -5% to +5%
        double changePercent = (random.nextDouble() - 0.5) * 0.1; // -0.05 to +0.05
        this.currentPrice = this.currentPrice * (1 + changePercent);
        
        // Ensure price doesn't go below $1
        if (this.currentPrice < 1.0) {
            this.currentPrice = 1.0;
        }
    }

    /**
     * Gets the price change percentage from previous price
     */
    public double getPriceChangePercent() {
        if (previousPrice == 0) return 0;
        return ((currentPrice - previousPrice) / previousPrice) * 100;
    }

    /**
     * Gets the absolute price change from previous price
     */
    public double getPriceChange() {
        return currentPrice - previousPrice;
    }

    // Getters and Setters
    public String getSymbol() {
        return symbol;
    }

    public String getCompanyName() {
        return companyName;
    }

    public double getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(double currentPrice) {
        this.previousPrice = this.currentPrice;
        this.currentPrice = currentPrice;
    }

    public double getPreviousPrice() {
        return previousPrice;
    }

    @Override
    public String toString() {
        return String.format("%s (%s) - $%.2f", companyName, symbol, currentPrice);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Stock stock = (Stock) obj;
        return symbol.equals(stock.symbol);
    }

    @Override
    public int hashCode() {
        return symbol.hashCode();
    }
}
