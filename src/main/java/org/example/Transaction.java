package org.example;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Represents a buy or sell transaction
 */
public class Transaction {
    public enum TransactionType {
        BUY, SELL
    }

    private String stockSymbol;
    private TransactionType type;
    private int quantity;
    private double pricePerShare;
    private LocalDateTime timestamp;
    private double totalAmount;

    public Transaction(String stockSymbol, TransactionType type, int quantity, double pricePerShare) {
        this.stockSymbol = stockSymbol;
        this.type = type;
        this.quantity = quantity;
        this.pricePerShare = pricePerShare;
        this.timestamp = LocalDateTime.now();
        this.totalAmount = quantity * pricePerShare;
    }

    // Getters
    public String getStockSymbol() {
        return stockSymbol;
    }

    public TransactionType getType() {
        return type;
    }

    public int getQuantity() {
        return quantity;
    }

    public double getPricePerShare() {
        return pricePerShare;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public String getFormattedTimestamp() {
        return timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    public String toString() {
        return String.format("%s %d shares of %s at $%.2f per share (Total: $%.2f) - %s",
                type.toString(), quantity, stockSymbol, pricePerShare, totalAmount, getFormattedTimestamp());
    }
}
