# Stock Trading Simulator

A comprehensive stock trading simulation application built with Java using Object-Oriented Programming principles. This application allows users to simulate stock trading with real-time market data, portfolio management, and performance tracking.

## Features

### Core Functionality
- **Market Data Display**: View real-time stock prices with price changes and percentage movements
- **Buy/Sell Operations**: Execute stock transactions with validation and confirmation
- **Portfolio Management**: Track holdings, average costs, and current values
- **Performance Tracking**: Monitor gains/losses and portfolio performance over time
- **Transaction History**: Complete record of all buy and sell transactions
- **Data Persistence**: Save and load user data using file I/O

### Market Simulation
- **Real-time Price Updates**: Stock prices update automatically every 10 seconds
- **Market Volatility**: Prices fluctuate between -5% to +5% to simulate market conditions
- **10 Pre-loaded Stocks**: AAPL, GOOGL, MSFT, AMZN, TSLA, META, NVDA, NFLX, AMD, INTC

### User Management
- **User Accounts**: Create new accounts or login to existing ones
- **Initial Balance**: Customizable starting balance (default $10,000)
- **Account Persistence**: User data saved to files for future sessions

## Object-Oriented Design

### Core Classes

1. **Stock.java**
   - Represents individual stocks with symbol, company name, and price
   - Handles price updates and change calculations
   - Implements price volatility simulation

2. **User.java**
   - Manages user account information and balance
   - Handles buy/sell operations
   - Calculates total account value and performance

3. **Portfolio.java**
   - Manages stock holdings and average costs
   - Tracks transaction history
   - Calculates portfolio value and performance metrics

4. **Transaction.java**
   - Represents individual buy/sell transactions
   - Stores transaction details with timestamps
   - Supports both BUY and SELL transaction types

5. **MarketData.java**
   - Manages all available stocks
   - Handles market-wide price updates
   - Provides formatted market data display

6. **TradingEngine.java**
   - Executes buy and sell orders with validation
   - Handles trade confirmations and error checking
   - Provides stock quotes and cost calculations

7. **PortfolioManager.java**
   - Displays portfolio summaries and performance metrics
   - Shows transaction history
   - Provides detailed portfolio analysis

8. **FileDataPersistence.java**
   - Handles file I/O operations for data persistence
   - Saves/loads user data, portfolio, and transaction history
   - Creates organized data directory structure

9. **TradingSimulator.java**
   - Main application class with user interface
   - Manages application flow and user interactions
   - Coordinates all system components

## Installation and Setup

### Prerequisites
- Java 21 or higher
- Command line access

### Compilation
```bash
# Compile all Java files
javac -d target/classes src/main/java/org/example/*.java
```

### Running the Application
```bash
# Run the main application
java -cp target/classes org.example.Main

# Run the test demonstration
java -cp target/classes org.example.TradingSimulatorTest
```

## Usage Guide

### Starting the Application
1. Run the main application
2. Choose to login with existing account or create new account
3. Enter username and initial balance (if creating new account)

### Main Menu Options
1. **View Market Data** - Display current stock prices and changes
2. **Get Stock Quote** - Get detailed information about a specific stock
3. **Buy Stocks** - Purchase shares of available stocks
4. **Sell Stocks** - Sell shares from your portfolio
5. **View Portfolio** - See your current holdings and performance
6. **View Transaction History** - Review all past transactions
7. **View Performance Metrics** - Detailed performance analysis
8. **Save Data** - Manually save your data to files
9. **Exit** - Exit the application (with option to save)

### Trading Operations

#### Buying Stocks
1. Select "Buy Stocks" from main menu
2. Enter stock symbol (e.g., AAPL, GOOGL)
3. Enter quantity to purchase
4. Review total cost and confirm purchase
5. Transaction will be executed if sufficient funds available

#### Selling Stocks
1. Select "Sell Stocks" from main menu
2. Enter stock symbol you own
3. Enter quantity to sell
4. Review total revenue and confirm sale
5. Transaction will be executed if sufficient shares available

### Portfolio Tracking
- **Current Value**: Real-time portfolio value based on current market prices
- **Cost Basis**: Total amount invested in stocks
- **Gain/Loss**: Difference between current value and cost basis
- **Performance Percentage**: Percentage gain or loss from initial investment

## Data Persistence

The application automatically saves data to the `trading_data` directory:
- `{username}_user_data.txt` - User account information
- `{username}_portfolio_data.txt` - Portfolio holdings and average costs
- `{username}_transactions.txt` - Complete transaction history

Data is automatically loaded when logging in with an existing username.

## Available Stocks

| Symbol | Company Name | Initial Price |
|--------|-------------|---------------|
| AAPL | Apple Inc. | $150.00 |
| GOOGL | Alphabet Inc. | $2,500.00 |
| MSFT | Microsoft Corporation | $300.00 |
| AMZN | Amazon.com Inc. | $3,200.00 |
| TSLA | Tesla Inc. | $800.00 |
| META | Meta Platforms Inc. | $320.00 |
| NVDA | NVIDIA Corporation | $450.00 |
| NFLX | Netflix Inc. | $400.00 |
| AMD | Advanced Micro Devices | $90.00 |
| INTC | Intel Corporation | $55.00 |

## Example Usage

### Sample Trading Session
```
1. Start with $10,000 balance
2. Buy 10 shares of AAPL at $150.00 = $1,500
3. Buy 2 shares of GOOGL at $2,500.00 = $5,000
4. Wait for market updates (prices change automatically)
5. Sell 5 shares of AAPL at new market price
6. View portfolio performance and transaction history
```

### Performance Metrics
- **Total Account Value**: Cash + Portfolio Value
- **Total Gain/Loss**: Current Value - Initial Balance
- **Portfolio Gain/Loss**: Current Portfolio Value - Cost Basis
- **Number of Holdings**: Count of different stocks owned
- **Transaction Count**: Total number of buy/sell operations

## Technical Features

### Market Simulation
- Automatic price updates every 10 seconds
- Random price fluctuations (-5% to +5%)
- Price floor protection (minimum $1.00)
- Real-time gain/loss calculations

### Error Handling
- Insufficient funds validation for purchases
- Insufficient shares validation for sales
- Invalid stock symbol detection
- Input validation for all user entries

### File Management
- Automatic directory creation
- Organized file structure
- Error handling for file operations
- Data integrity validation

## Testing

Run the test class to see a demonstration of all features:
```bash
java -cp target/classes org.example.TradingSimulatorTest
```

This will demonstrate:
- Market data display
- Buy/sell operations
- Portfolio management
- Market price updates
- Performance tracking
- File persistence

## Future Enhancements

Potential improvements for the system:
- Database integration for better data persistence
- More sophisticated market simulation algorithms
- Additional stock analysis tools
- Graphical user interface
- Real market data integration
- Advanced order types (limit orders, stop-loss)
- Portfolio diversification analysis
- Historical performance charts

## License

This project is created for educational purposes as part of a Programming Technology assignment.
